# 🎨 مخطط بصري لأنماط التصميم

```
                    🏛️ DESIGN PATTERNS 🏛️
                         أنماط التصميم
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
   🏭 CREATIONAL         🎭 BEHAVIORAL        🏗️ STRUCTURAL
   الأنماط الإنشائية      الأنماط السلوكية       الأنماط الهيكلية
        │                     │                     │
        │                     │                     │
   ┌────┴────┐           ┌────┴────┐           ┌────┴────┐
   │         │           │         │           │         │
🎯 Prototype │      🎯 Strategy  🎯 State   🎯 Facade  🎯 Adapter
   │         │           │         │           │         │
   │    ┌────┴────┐      │    ┌────┴────┐      │    ┌────┴────┐
   │    │ Factory │      │    │ Observer│      │    │ Bridge  │
   │    │ Builder │      │    │ Command │      │    │ Composite│
   │    │Singleton│      │    │Template │      │    │Flyweight│
   │    └─────────┘      │    │Iterator │      │    └─────────┘
   │                     │    │ Visitor │      │
   └─────────────────────┘    │ Mediator│      └─🎯 Decorator
                              │ Memento │      └─ 🎯 Proxy
                              │Chain of │
                              │Respons. │
                              └─────────┘
```

---

## 🎯 الأنماط المطلوبة في الاختبار

### 🏭 Creational Pattern

```
┌─────────────────────────────────────────────────────────────┐
│                    🎯 PROTOTYPE PATTERN                     │
├─────────────────────────────────────────────────────────────┤
│  💡 المفهوم: استنساخ الكائنات بدلاً من إنشائها من الصفر   │
│                                                             │
│  🔧 الاستخدام:                                              │
│  • عندما يكون الإنشاء مكلف 💰                              │
│  • عندما نريد نماذج أولية 📋                              │
│  • عندما نريد تجنب التعقيد 🔄                             │
│                                                             │
│  🏗️ الهيكل:                                                │
│     IPrototype ──→ Clone()                                  │
│         │                                                   │
│         ▼                                                   │
│  ConcretePrototype ──→ Clone() + Data                       │
│         │                                                   │
│         ▼                                                   │
│  PrototypeManager ──→ Register() + ClonePrototype()         │
│                                                             │
│  🎯 مثال: نظام إدارة الوثائق                              │
│  reportTemplate.Clone() → januaryReport                     │
└─────────────────────────────────────────────────────────────┘
```

### 🎭 Behavioral Patterns

```
┌─────────────────────────────────────────────────────────────┐
│                    🎯 STRATEGY PATTERN                      │
├─────────────────────────────────────────────────────────────┤
│  💡 المفهوم: عائلة خوارزميات قابلة للتبديل                 │
│                                                             │
│  🔧 الاستخدام:                                             │
│  • خوارزميات متعددة لنفس المهمة 🔄                         │
│  • تجنب if/else المعقدة ❌                                  │
│  • إضافة خوارزميات جديدة بسهولة ➕                         │
│                                                             │
│  🏗️ الهيكل:                                                │
│     IStrategy ──→ Execute()                                 │
│         │                                                   │
│         ├── CreditCardStrategy                              │
│         ├── PayPalStrategy                                  │
│         └── BankTransferStrategy                            │
│                                                             │
│     Context ──→ SetStrategy() + ExecuteStrategy()           │
│                                                             │
│  🎯 مثال: نظام الدفع الإلكتروني                            │
│  paymentProcessor.SetStrategy(new PayPalStrategy())         │
└─────────────────────────────────────────────────────────────┘
```

```
┌─────────────────────────────────────────────────────────────┐
│                     🎯 STATE PATTERN                        │
├─────────────────────────────────────────────────────────────┤
│  💡 المفهوم: تغيير سلوك الكائن حسب حالته الداخلية          │
│                                                             │
│  🔧 الاستخدام:                                             │
│  • سلوك يعتمد على الحالة 🔄                                │
│  • تجنب if/else للحالات ❌                                  │
│  • حالات كثيرة ومعقدة 🔀                                   │
│                                                             │
│  🏗️ الهيكل:                                                │
│     IState ──→ Handle()                                     │
│         │                                                   │
│         ├── IdleState                                       │
│         ├── HasMoneyState                                   │
│         ├── ProductSelectedState                            │
│         └── DispensingState                                 │
│                                                             │
│     Context ──→ SetState() + Request()                      │
│                                                             │
│  🎯 مثال: آلة بيع المشروبات                                │
│  machine.InsertCoin() → HasMoneyState                       │
└─────────────────────────────────────────────────────────────┘
```

### 🏗️ Structural Patterns

```
┌─────────────────────────────────────────────────────────────┐
│                     🎯 FACADE PATTERN                       │
├─────────────────────────────────────────────────────────────┤
│  💡 المفهوم: واجهة مبسطة لنظام معقد                      │
│                                                             │
│  🔧 الاستخدام:                                             │
│  • تبسيط التعامل مع النظم المعقدة 🎭                     │
│  • إخفاء التعقيد عن العميل 🔒                             │
│  • توحيد نقطة الدخول 🚪                                   │
│                                                             │
│  🏗️ الهيكل:                                                │
│     Facade ──→ SimpleOperation()                            │
│         │                                                   │
│         ├── SubSystem1.Operation1()                         │
│         ├── SubSystem2.Operation2()                         │
│         └── SubSystem3.Operation3()                         │
│                                                             │
│  🎯 مثال: نظام المنزل الذكي                                │
│  smartHome.WakeUpMode() → lights + music + coffee          │
└─────────────────────────────────────────────────────────────┘
```

```
┌─────────────────────────────────────────────────────────────┐
│                    🎯 ADAPTER PATTERN                       │
├─────────────────────────────────────────────────────────────┤
│  💡 المفهوم: ربط واجهات غير متوافقة                       │
│                                                             │
│  🔧 الاستخدام:                                             │
│  • دمج أنظمة قديمة مع جديدة 🔄                             │
│  • استخدام مكتبات خارجية 📚                                │
│  • تحويل البيانات بين الأنظمة 🔀                           │
│                                                             │
│  🏗️ الهيكل:                                                │
│     ITarget ──→ Request()                                   │
│         │                                                   │
│         ▼                                                   │
│     Adapter ──→ Request() + SpecificRequest()               │
│         │                                                   │
│         ▼                                                   │
│     Adaptee ──→ SpecificRequest()                           │
│                                                             │
│  🎯 مثال: نظام الدفع القديم مع الجديد                      │
│  newPayment.Pay() → oldPayment.MakePayment()               │
└─────────────────────────────────────────────────────────────┘
```

```
┌─────────────────────────────────────────────────────────────┐
│                   🎯 DECORATOR PATTERN                      │
├─────────────────────────────────────────────────────────────┤
│  💡 المفهوم: إضافة وظائف جديدة للكائنات ديناميكياً        │
│                                                             │
│  🔧 الاستخدام:                                             │
│  • إضافة ميزات بدون تعديل الكود الأصلي ➕                  │
│  • تجنب الوراثة المعقدة 🚫                                 │
│  • مرونة في التركيب 🔧                                     │
│                                                             │
│  🏗️ الهيكل:                                                │
│     IComponent ──→ Operation()                              │
│         │                                                   │
│         ├── ConcreteComponent                               │
│         └── BaseDecorator ──→ Operation()                   │
│                 │                                           │
│                 ├── ConcreteDecoratorA                      │
│                 └── ConcreteDecoratorB                      │
│                                                             │
│  🎯 مثال: نظام القهوة                                      │
│  coffee = new Milk(new Sugar(new Coffee()))                │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎯 مقارنة سريعة

| النمط |                    النوع |          الهدف |           مثال |
| ---------------|---------------- |--------------------------|--------|
| 🎯 Prototype   | إنشائي |       استنساخ الكائنات |    نسخ الوثائق |
| 🎯 Strategy    | سلوكي |      خوارزميات متبادلة |      طرق الدفع  |
| 🎯 State       | سلوكي |   تغيير السلوك بالحالة |      آلة البيع  |
| 🎯 Facade      | هيكلي |    تبسيط النظم المعقدة |   المنزل الذكي  |
| 🎯 Adapter     | هيكلي |            ربط الواجهات |     دمج الأنظمة  |
| 🎯 Decorator   | هيكلي |             إضافة وظائف |   تخصيص القهوة  |

---

## 💡 نصائح للاختبار

```
🎯 للحفظ السريع:
┌─────────────────────────────────────────┐
│ Prototype = نسخ 📋                      │
│ Strategy = خيارات 🔄                    │
│ State = حالات 🔀                        │
│ Facade = تبسيط 🎭                       │
│ Adapter = ربط 🔗                        │
│ Decorator = إضافة ➕                     │
│ Proxy = وكيل 🛡️                         │
└─────────────────────────────────────────┘
```

---

# 📚 ملخص شامل للمحاضرة

## 🎯 تعريف أنماط التصميم

### المفهوم الأساسي:
- **النمط**: مثل الوصفة، لكن المكونات هي مبادئ البرمجة الكائنية
- **الهدف**: وصف حل لمشكلة تتكرر باستمرار في البيئة البرمجية
- **المرونة**: يمكن استخدام الحل مليون مرة دون تكراره بنفس الطريقة

### مثال توضيحي:
```csharp
interface IBaseInterface { }
class Derived : IBaseInterface { }
```
هذا مثال على تطبيق نمط Bridge باستخدام الواجهات بدلاً من الفئات.

---

## 📊 أنواع أنماط التصميم

### 1. الأنماط الهيكلية (Structural Patterns)
**الهدف**: ربط الكائنات الموجودة معاً
- **Façade**: واجهة موحدة للنظام المعقد
- **Adapter**: ربط الواجهات غير المتوافقة
- **Bridge**: فصل التجريد عن التنفيذ
- **Decorator**: إضافة وظائف جديدة للكائنات

### 2. الأنماط السلوكية (Behavioral Patterns)
**الهدف**: توفير طرق للسلوك المرن والمتغير
- **Strategy**: احتواء التنوع في السلوك

### 3. الأنماط الإنشائية (Creational Patterns)
**الهدف**: إنشاء وتكوين الكائنات
- **Abstract Factory**: إنشاء عائلات من الكائنات
- **Singleton**: ضمان وجود نسخة واحدة فقط
- **Factory Method**: إنشاء الكائنات دون تحديد فئتها الدقيقة
- **Double-Checked Locking**: تحسين أداء Singleton

---

## 🏛️ نمط الواجهة (Facade Pattern)

### التعريف:
توفير واجهة موحدة لمجموعة من الواجهات في نظام فرعي، مما يجعل النظام أسهل في الاستخدام.

### المشاركون:
- **Facade**: يعرف أي فئات النظام الفرعي مسؤولة عن الطلب
- **Subsystem Classes**: تنفذ وظائف النظام الفرعي

### المميزات:
- **البساطة**: تبسيط استخدام النظام المعقد
- **العزل**: فصل العميل عن النظام الفرعي
- **التحكم**: تقليل عدد الكائنات التي يتعامل معها العميل

### مثال عملي:
```csharp
// بدلاً من التعامل مع Database, Model, Element منفصلة
// نستخدم Database-Façade واحدة
DatabaseFacade facade = new DatabaseFacade();
facade.GetInformation(); // عملية واحدة بسيطة
```

### كود المثال:
```csharp
class Facade {
    private SubSystemOne _one;
    private SubSystemTwo _two;
    private SubSystemThree _three;
    private SubSystemFour _four;

    public void MethodA() {
        _one.MethodOne();
        _two.MethodTwo();
        _four.MethodFour();
    }

    public void MethodB() {
        _two.MethodTwo();
        _three.MethodThree();
    }
}
```

---

## 🔌 نمط المحول (Adapter Pattern)

### التعريف:
نمط متعدد الاستخدامات يربط بين الأنواع التي لم تُصمم للعمل معاً، خاصة مع الكود القديم.

### الدور:
- تمكين النظام من استخدام فئات واجهاتها لا تتطابق مع متطلباته
- مفيد جداً مع المكتبات الجاهزة والأدوات الخارجية
- يتضمن تغييرات في المدخلات والمخرجات

### المكونات:
- **ITarget**: الواجهة التي يريد العميل استخدامها
- **Adaptee**: التنفيذ الذي يحتاج للتكيف
- **Adapter**: الفئة التي تنفذ ITarget باستخدام Adaptee

### مثال من الواقع:
انتقال أجهزة Apple من معالجات PowerPC إلى Intel Core Duo في 2006

### كود المثال:
```csharp
class Adapter : Adaptee, ITarget {
    public string Request(int i) {
        return "Rough estimate is " +
               (int)Math.Round(SpecificRequest(i,3));
    }
}
```

---

## 🎨 نمط المُزخرِف (Decorator Pattern)

### الدور:
توفير طريقة لإرفاق حالة وسلوك جديد للكائن بشكل ديناميكي.

### النقطة الأساسية:
المُزخرِفات ترث من الفئة الأصلية وتحتوي على نسخة منها.

### المميزات:
- **عدم الوعي**: الكائن الأصلي لا يعرف بالزخارف
- **عدم التعقيد**: لا توجد فئة كبيرة تحتوي على جميع الخيارات
- **الاستقلالية**: الزخارف مستقلة عن بعضها
- **التركيب**: يمكن دمج الزخارف بطرق مختلفة

### المكونات:
- **Component**: الفئة الأصلية للكائنات
- **IComponent**: الواجهة التي تحدد الكائنات القابلة للزخرفة
- **Decorator**: فئة تتوافق مع IComponent وتضيف حالة/سلوك

### مثال توضيحي:
إضافة إطار أو علامات للصورة دون تعديل الصورة الأصلية.

### العلاقات:
- **Is-a relationship**: Decorator يحقق IComponent
- **Has-a relationship**: Decorator يحتوي على IComponent

---

## 🛡️ نمط الوكيل (Proxy Pattern)

### الدور:
دعم الكائنات التي تتحكم في إنشاء والوصول إلى كائنات أخرى.

### المفهوم:
الوكيل كائن صغير (عام) يمثل كائناً أكثر تعقيداً (خاص) يتم تفعيله عند توفر ظروف معينة.

### المكونات:
- **ISubject**: واجهة مشتركة للموضوعات والوكلاء
- **Subject**: الفئة التي يمثلها الوكيل
- **Proxy**: فئة تنشئ وتتحكم وتحسن الوصول للموضوع

### أنواع الوكلاء:
1. **Virtual Proxies**: تأجيل إنشاء الكائن
2. **Authentication Proxies**: فحص صلاحيات الوصول
3. **Remote Proxies**: ترميز الطلبات وإرسالها عبر الشبكة
4. **Smart Proxies**: إضافة أو تغيير الطلبات

### مثال من الواقع:
أنظمة الشبكات الاجتماعية حيث يتم:
- تأجيل إنشاء البيئة الغنية (virtual proxy)
- تسجيل دخول المستخدمين (authentication proxy)
- إرسال الطلبات عبر الشبكة (remote proxy)
- تنفيذ إجراءات على كتب الأصدقاء (smart proxy)

### كود المثال:
```csharp
class Proxy : Subject {
    private RealSubject _realSubject;

    public override void Request() {
        // استخدام Lazy Initialization
        if (_realSubject == null) {
            _realSubject = new RealSubject();
        }
        _realSubject.Request();
    }
}
```

---

## 🎯 الخلاصة الرئيسية

### الفوائد العامة لأنماط التصميم:
1. **إعادة الاستخدام**: حلول مُجربة ومُثبتة
2. **التواصل**: لغة مشتركة بين المطورين
3. **الجودة**: تحسين جودة وهيكلية الكود
4. **المرونة**: سهولة التعديل والتوسع

### متى نستخدم كل نمط:
- **Facade**: عندما نريد تبسيط واجهة نظام معقد
- **Adapter**: عندما نحتاج لربط واجهات غير متوافقة
- **Decorator**: عندما نريد إضافة وظائف دون تعديل الكود الأصلي
- **Proxy**: عندما نحتاج للتحكم في الوصول للكائنات

### نصائح التطبيق:
- استخدم الواجهات بدلاً من الفئات عند الإمكان
- فكر في المرونة والقابلية للتوسع
- اختر النمط المناسب للمشكلة المحددة
- لا تُفرط في استخدام الأنماط

### أسئلة مهمة للمراجعة:
1. **لماذا نستخدم الواجهات بدلاً من الفئات في الأنماط؟**
   - لأن الأنماط تعرّف أفضل الممارسات المُثبتة عملياً

2. **ما الفرق بين Adapter و Facade؟**
   - Adapter: يربط واجهات غير متوافقة
   - Facade: يبسط واجهة نظام معقد

3. **كيف يختلف Decorator عن الوراثة التقليدية؟**
   - Decorator يضيف وظائف ديناميكياً دون تعديل الكود الأصلي
   - الوراثة تتطلب تعديل الهيكل الأساسي

4. **متى نحتاج لاستخدام Proxy Pattern؟**
   - عندما نريد التحكم في الوصول للكائنات
   - عندما نريد تأجيل إنشاء الكائنات المكلفة
   - عندما نحتاج لإضافة طبقة أمان أو مراقبة