# 📚 ملخص شامل - محاضرة أنماط التصميم (Design Patterns)

## 🎯 المقدمة والتعريف الأساسي

### ما هي أنماط التصميم؟
- **التعريف**: أنماط التصميم مثل الوصفات، لكن المكونات هي مبادئ البرمجة الكائنية
- **الهدف**: وصف حل لمشكلة تتكرر باستمرار في البيئة البرمجية
- **المرونة**: يمكن استخدام الحل مليون مرة دون تكراره بنفس الطريقة

### مثال توضيحي أساسي:
```csharp
interface IBaseInterface { }
class Derived : IBaseInterface { }
```
هذا مثال كلاسيكي على تطبيق نمط Bridge

### لماذا الواجهات وليس الفئات؟
لأن الأنماط تعرّف أفضل الممارسات المُثبتة عملياً في تطوير التطبيقات

---

## 📊 تصنيف أنماط التصميم

### 1. 🏗️ الأنماط الهيكلية (Structural Patterns)
**الهدف**: ربط الكائنات الموجودة معاً
- **Façade**: التعامل مع الواجهات
- **Adapter**: التعامل مع الواجهات
- **Bridge**: ربط التنفيذ بالتجريد
- **Decorator**: ربط التنفيذ بالتجريد

### 2. 🎭 الأنماط السلوكية (Behavioral Patterns)
**الهدف**: توفير طرق للسلوك المرن والمتغير
- **Strategy**: احتواء التنوع في السلوك

### 3. 🏭 الأنماط الإنشائية (Creational Patterns)
**الهدف**: إنشاء وتكوين الكائنات
- **Abstract Factory**: إنشاء عائلات من الكائنات
- **Singleton**: ضمان وجود نسخة واحدة فقط
- **Double-Checked Locking**: تحسين أداء Singleton
- **Factory Method**: إنشاء الكائنات

---

## 🏛️ نمط الواجهة (Facade Pattern)

### التعريف والهدف:
- توفير واجهة موحدة لمجموعة من الواجهات في نظام فرعي
- جعل النظام الفرعي أسهل في الاستخدام
- عزل العملاء عن النظام الفرعي

### المشاركون في النمط:
1. **Facade (مثل: Mortgage Application)**
   - يعرف أي فئات النظام الفرعي مسؤولة عن الطلب
   - يفوض طلبات العميل للكائنات المناسبة

2. **Subsystem Classes (مثل: Bank, Credit, Loan)**
   - تنفذ وظائف النظام الفرعي
   - تتعامل مع العمل المُوكل من Facade
   - لا تعرف شيئاً عن Facade ولا تحتفظ بمرجع إليه

### خصائص النمط:
- **القصد**: تبسيط استخدام نظام موجود وتعريف واجهة خاصة
- **المشكلة**: الحاجة لاستخدام جزء فقط من نظام معقد
- **الحل**: Facade يقدم واجهة جديدة للعميل
- **النتائج**: يبسط استخدام النظام لكن قد يحد من بعض الوظائف

### مثال عملي:
```csharp
// بدلاً من: Client → Database → Model → Element
// نستخدم: Client → Database-Façade
DatabaseFacade facade = new DatabaseFacade();
facade.GetInformation(); // عملية واحدة بسيطة
```

### كود المثال الكامل:
```csharp
class Facade {
    private SubSystemOne _one;
    private SubSystemTwo _two;
    private SubSystemThree _three;
    private SubSystemFour _four;
    
    public Facade() {
        _one = new SubSystemOne();
        _two = new SubSystemTwo();
        _three = new SubSystemThree();
        _four = new SubSystemFour();
    }
    
    public void MethodA() {
        Console.WriteLine("\nMethodA() ---- ");
        _one.MethodOne();
        _two.MethodTwo();
        _four.MethodFour();
    }
    
    public void MethodB() {
        Console.WriteLine("\nMethodB() ---- ");
        _two.MethodTwo();
        _three.MethodThree();
    }
}
```

---

## 🔌 نمط المحول (Adapter Pattern)

### التعريف والدور:
- نمط متعدد الاستخدامات يربط بين الأنواع التي لم تُصمم للعمل معاً
- مفيد جداً مع الكود القديم (Legacy Code)
- يتضمن تغييرات في المدخلات والمخرجات

### أنواع المحولات:
- **Class Adapters**: محولات الفئات
- **Object Adapters**: محولات الكائنات  
- **Two-way Adapters**: محولات ثنائية الاتجاه
- **Pluggable Adapters**: محولات قابلة للتوصيل

### دور النمط:
1. تمكين النظام من استخدام فئات واجهاتها لا تتطابق مع متطلباته
2. مفيد للكود الجاهز والمكتبات والأدوات
3. قبول استدعاءات من التطبيق وتحويلها لاستدعاءات على طرق المكتبة

### مثال من الواقع:
**انتقال Apple من PowerPC إلى Intel (2006)**:
- من 1996-2006: أجهزة Mac تعمل على معالجات PowerPC مع Mac OS X
- أبريل 2006: Apple بدأت إصدار أجهزة جديدة بمعالجات Intel Core Duo
- تم إعادة كتابة Mac OS X للمعالج الجديد
- المستخدمون وصلوا للبرامج الموجودة عبر أنظمة تشغيل أخرى

### تصميم النمط:
- **ITarget**: الواجهة التي يريد العميل استخدامها
- **Adaptee**: التنفيذ الذي يحتاج للتكيف  
- **Adapter**: الفئة التي تنفذ ITarget باستخدام Adaptee
- **Request**: العملية التي يريدها العميل
- **SpecificRequest**: تنفيذ وظيفة Request في Adaptee

### كود المثال:
```csharp
class Adaptee {
    // توفير دقة كاملة
    public double SpecificRequest(double a, double b) {
        return a/b;
    }
}

interface ITarget {
    // تقدير تقريبي مطلوب
    string Request(int i);
}

class Adapter : Adaptee, ITarget {
    public string Request(int i) {
        return "Rough estimate is " + 
               (int)Math.Round(SpecificRequest(i,3));
    }
}
```

---

## 🎨 نمط المُزخرِف (Decorator Pattern)

### الدور والهدف:
- توفير طريقة لإرفاق حالة وسلوك جديد للكائن ديناميكياً
- **النقطة الأساسية**: المُزخرِفات ترث من الفئة الأصلية وتحتوي على نسخة منها

### مثال توضيحي:
إضافة ميزات للصورة (إطار، علامات) دون تعديل الصورة الأصلية

### جمال هذا النمط:
1. **عدم الوعي**: الكائن الأصلي لا يعرف بأي زخارف
2. **عدم التعقيد**: لا توجد فئة كبيرة تشمل جميع خيارات الميزات
3. **الاستقلالية**: الزخارف مستقلة عن بعضها البعض
4. **التركيب**: يمكن تركيب الزخارف معاً بطريقة مرنة

### تصميم النمط:
- **Component**: فئة أصلية للكائنات يمكن إضافة أو تعديل عملياتها
- **Operation**: عملية في كائنات IComponent يمكن استبدالها
- **IComponent**: الواجهة التي تحدد فئات الكائنات القابلة للزخرفة
- **Decorator**: فئة تتوافق مع واجهة IComponent وتضيف حالة/سلوك

### العلاقات في النمط:
1. **علاقة Is-a**: Decorator يحقق واجهة IComponent
2. **علاقة Has-a**: Decorator يحتوي على كائن IComponent

### مكونات متعددة:
- يمكن زخرفة مكونات مختلفة تتوافق مع الواجهة
- مثال: رسم أشخاص، منازل، سفن من أشكال وخطوط بسيطة

### مُزخرِفات متعددة:
- يمكن إنشاء نسخ مختلفة من مُزخرِف Tag
- يمكن وجود أنواع أخرى من المُزخرِفات (Border, Invisible)
- كل مُزخرِف يحتوي على كائن component قد يكون بحد ذاته مُزخرِف

### عمليات متعددة:
- التركيز على الرسم كعملية رئيسية للصور والزخارف
- عمليات أخرى قد تكون جزءاً من المكون الأصلي وواجهته
- بعض العمليات قد تكون سلوكيات مضافة في مُزخرِفات معينة فقط

---

## 🛡️ نمط الوكيل (Proxy Pattern)

### الدور والمفهوم:
- دعم الكائنات التي تتحكم في إنشاء والوصول إلى كائنات أخرى
- الوكيل كائن صغير (عام) يقف مكان كائن أكثر تعقيداً (خاص)
- يتم تفعيل الكائن المعقد عند توضح ظروف معينة

### مثال من الواقع:
ظاهرة ارتفاع شعبية أنظمة الشبكات الاجتماعية

### تصميم النمط:
- **ISubject**: واجهة مشتركة للموضوعات والوكلاء تمكن استخدامهما بالتبادل
- **Subject**: الفئة التي يمثلها الوكيل
- **Proxy**: فئة تنشئ وتتحكم وتحسن وتصادق على الوصول للموضوع
- **Request**: عملية على الموضوع يتم توجيهها عبر الوكيل

### أنواع الوكلاء:
1. **Virtual Proxies (الوكلاء الافتراضيون)**:
   - تسليم إنشاء الكائن لكائن آخر
   - مفيد إذا كانت عملية الإنشاء بطيئة أو غير ضرورية

2. **Authentication Proxies (وكلاء المصادقة)**:
   - فحص صحة صلاحيات الوصول للطلب

3. **Remote Proxies (الوكلاء البعيدون)**:
   - ترميز الطلبات وإرسالها عبر الشبكة

4. **Smart Proxies (الوكلاء الأذكياء)**:
   - إضافة أو تغيير الطلبات قبل إرسالها

### تطبيق في الشبكات الاجتماعية:
- **Virtual proxy**: تأجيل إنشاء بيئة غنية
- **Authentication proxy**: تسجيل دخول المستخدمين  
- **Remote proxy**: إرسال الطلبات عبر الشبكة
- **Smart proxy**: تنفيذ إجراءات على كتب الأصدقاء

### المشاركون في النمط:
1. **Proxy (مثل: MathProxy)**:
   - يحتفظ بمرجع يسمح للوكيل بالوصول للموضوع الحقيقي
   - يوفر واجهة مطابقة للموضوع
   - يتحكم في الوصول ويكون مسؤولاً عن الإنشاء والحذف

2. **Subject (مثل: IMath)**:
   - يعرّف الواجهة المشتركة للموضوع الحقيقي والوكيل

3. **RealSubject (مثل: Math)**:
   - يعرّف الكائن الحقيقي الذي يمثله الوكيل

### كود المثال:
```csharp
class Proxy : Subject {
    private RealSubject _realSubject;
    
    public override void Request() {
        // استخدام Lazy Initialization
        if (_realSubject == null) {
            _realSubject = new RealSubject();
        }
        _realSubject.Request();
    }
}
```

---

## 🎯 الخلاصة والنصائح العملية

### الفوائد العامة لأنماط التصميم:
1. **إعادة الاستخدام**: حلول مُجربة ومُثبتة
2. **التواصل**: لغة مشتركة بين المطورين
3. **الجودة**: تحسين جودة وهيكلية الكود  
4. **المرونة**: سهولة التعديل والتوسع

### متى نستخدم كل نمط:
- **Facade**: تبسيط واجهة نظام معقد
- **Adapter**: ربط واجهات غير متوافقة
- **Decorator**: إضافة وظائف دون تعديل الكود الأصلي
- **Proxy**: التحكم في الوصول للكائنات

### نصائح التطبيق:
- استخدم الواجهات بدلاً من الفئات عند الإمكان
- فكر في المرونة والقابلية للتوسع
- اختر النمط المناسب للمشكلة المحددة
- لا تُفرط في استخدام الأنماط

### أسئلة مراجعة مهمة:
1. **لماذا الواجهات بدلاً من الفئات؟** - لأن الأنماط تعرّف أفضل الممارسات المُثبتة
2. **الفرق بين Adapter و Facade؟** - Adapter يربط، Facade يبسط
3. **ميزة Decorator على الوراثة؟** - إضافة ديناميكية دون تعديل الأصل
4. **متى نحتاج Proxy؟** - للتحكم في الوصول وتأجيل الإنشاء المكلف

---

## 💡 أمثلة عملية إضافية

### 🏛️ مثال Facade - نظام المنزل الذكي:
```csharp
class SmartHomeFacade {
    private LightSystem lights;
    private MusicSystem music;
    private CoffeeSystem coffee;
    private SecuritySystem security;

    public void WakeUpMode() {
        lights.TurnOn();
        music.PlayMorningPlaylist();
        coffee.StartBrewing();
        security.DisableNightMode();
    }

    public void SleepMode() {
        lights.DimAll();
        music.Stop();
        coffee.TurnOff();
        security.EnableNightMode();
    }
}
```

### 🔌 مثال Adapter - نظام الدفع:
```csharp
// النظام القديم
class OldPaymentSystem {
    public void MakePayment(string account, double amount) {
        Console.WriteLine($"Old system: Paid {amount} from {account}");
    }
}

// الواجهة الجديدة المطلوبة
interface INewPaymentSystem {
    bool ProcessPayment(PaymentInfo info);
}

// المحول
class PaymentAdapter : INewPaymentSystem {
    private OldPaymentSystem oldSystem;

    public PaymentAdapter(OldPaymentSystem old) {
        oldSystem = old;
    }

    public bool ProcessPayment(PaymentInfo info) {
        oldSystem.MakePayment(info.Account, info.Amount);
        return true;
    }
}
```

### 🎨 مثال Decorator - نظام القهوة:
```csharp
interface ICoffee {
    string GetDescription();
    double GetCost();
}

class SimpleCoffee : ICoffee {
    public string GetDescription() => "Simple Coffee";
    public double GetCost() => 2.0;
}

class MilkDecorator : ICoffee {
    private ICoffee coffee;

    public MilkDecorator(ICoffee coffee) {
        this.coffee = coffee;
    }

    public string GetDescription() => coffee.GetDescription() + ", Milk";
    public double GetCost() => coffee.GetCost() + 0.5;
}

class SugarDecorator : ICoffee {
    private ICoffee coffee;

    public SugarDecorator(ICoffee coffee) {
        this.coffee = coffee;
    }

    public string GetDescription() => coffee.GetDescription() + ", Sugar";
    public double GetCost() => coffee.GetCost() + 0.2;
}

// الاستخدام:
// ICoffee coffee = new SugarDecorator(new MilkDecorator(new SimpleCoffee()));
```

### 🛡️ مثال Proxy - تحميل الصور:
```csharp
interface IImage {
    void Display();
}

class RealImage : IImage {
    private string filename;

    public RealImage(string filename) {
        this.filename = filename;
        LoadImageFromDisk();
    }

    private void LoadImageFromDisk() {
        Console.WriteLine($"Loading image: {filename}");
        // عملية تحميل مكلفة
        Thread.Sleep(2000);
    }

    public void Display() {
        Console.WriteLine($"Displaying image: {filename}");
    }
}

class ProxyImage : IImage {
    private RealImage realImage;
    private string filename;

    public ProxyImage(string filename) {
        this.filename = filename;
    }

    public void Display() {
        // Lazy Loading - تحميل فقط عند الحاجة
        if (realImage == null) {
            realImage = new RealImage(filename);
        }
        realImage.Display();
    }
}
```

---

## 📋 جدول مقارنة شامل

| الخاصية | Facade | Adapter | Decorator | Proxy |
|---------|--------|---------|-----------|-------|
| **الهدف الرئيسي** | تبسيط النظام | ربط الواجهات | إضافة وظائف | التحكم في الوصول |
| **نوع المشكلة** | تعقيد النظام | عدم التوافق | الحاجة لميزات جديدة | الحاجة للتحكم |
| **العلاقة مع الكائن** | يحتوي على عدة كائنات | يحول واجهة واحدة | يلف كائن واحد | يمثل كائن واحد |
| **وقت الإنشاء** | عند إنشاء Facade | عند إنشاء Adapter | عند إنشاء Decorator | قد يؤجل الإنشاء |
| **مثال من الواقع** | ريموت التلفزيون | محول الكهرباء | إكسسوارات الهاتف | حارس الأمن |
| **التعقيد** | منخفض | متوسط | متوسط | متوسط إلى عالي |

---

## 🎯 نصائح للاختبار والمراجعة

### للحفظ السريع:
- **Facade** = واجهة موحدة = تبسيط 🎭
- **Adapter** = محول = ربط غير المتوافق 🔗
- **Decorator** = مُزخرِف = إضافة ميزات ➕
- **Proxy** = وكيل = تحكم في الوصول 🛡️

### أسئلة اختبار محتملة:
1. **اذكر الفرق بين Facade و Adapter مع مثال لكل منهما**
2. **متى نستخدم Decorator بدلاً من الوراثة؟**
3. **ما هي أنواع Proxy المختلفة؟ اذكر مثال لكل نوع**
4. **لماذا نفضل الواجهات على الفئات في أنماط التصميم؟**
5. **اكتب كود بسيط يوضح تطبيق نمط Adapter**

### نقاط مهمة للتذكر:
- كل نمط يحل مشكلة محددة
- الأنماط ليست قوانين بل إرشادات
- يمكن دمج عدة أنماط في نفس التطبيق
- الهدف هو تحسين جودة الكود وسهولة الصيانة
- لا تستخدم نمط إلا إذا كان يحل مشكلة حقيقية

---

## 📚 المراجع والمصادر الإضافية

### كتب مُوصى بها:
- "Design Patterns: Elements of Reusable Object-Oriented Software" - Gang of Four
- "Head First Design Patterns" - Freeman & Robson

### مواقع مفيدة:
- DoFactory Design Pattern Framework
- Refactoring.Guru Design Patterns
- Microsoft Docs - Design Patterns

### نصيحة أخيرة:
أنماط التصميم أدوات قوية، لكن الإفراط في استخدامها قد يعقد الكود أكثر من تبسيطه. استخدمها بحكمة عندما تواجه المشاكل التي صُممت لحلها.

---

## 🛡️ تفاصيل إضافية لنمط الوكيل (Proxy Pattern)

### المسؤوليات التفصيلية للوكيل:

#### 1. **الوكيل العام (Proxy)**:
- **الاحتفاظ بمرجع**: يسمح للوكيل بالوصول للموضوع الحقيقي
- **واجهة مطابقة**: يوفر واجهة مطابقة للموضوع حتى يمكن استبداله
- **التحكم في الوصول**: يتحكم في الوصول ويكون مسؤولاً عن الإنشاء والحذف

#### 2. **مسؤوليات حسب نوع الوكيل**:

**🌐 Remote Proxies (الوكلاء البعيدون)**:
- ترميز الطلب ومعاملاته
- إرسال الطلب المُرمز للموضوع الحقيقي في مساحة عنوان مختلفة

**💾 Virtual Proxies (الوكلاء الافتراضيون)**:
- تخزين معلومات إضافية مؤقتاً عن الموضوع الحقيقي
- تأجيل الوصول إليه (مثل ImageProxy الذي يخزن أبعاد الصورة الحقيقية)

**🔒 Protection Proxies (وكلاء الحماية)**:
- فحص أن المُستدعي لديه صلاحيات الوصول المطلوبة لتنفيذ الطلب

### الكود الكامل لنمط Proxy:

```csharp
/// <summary>
/// Proxy Design Pattern Implementation
/// </summary>
class MainApp
{
    static void Main()
    {
        // Create proxy and request a service
        Proxy proxy = new Proxy();
        proxy.Request();

        // Wait for user
        Console.ReadKey();
    }
}

/// <summary>
/// The 'Subject' abstract class
/// </summary>
abstract class Subject
{
    public abstract void Request();
}

/// <summary>
/// The 'RealSubject' class
/// </summary>
class RealSubject : Subject
{
    public override void Request()
    {
        Console.WriteLine("Called RealSubject.Request()");
    }
}

/// <summary>
/// The 'Proxy' class
/// </summary>
class Proxy : Subject
{
    private RealSubject _realSubject;

    public override void Request()
    {
        // Use 'lazy initialization'
        if (_realSubject == null)
        {
            _realSubject = new RealSubject();
        }
        _realSubject.Request();
    }
}
```

---

## 🎭 الأنماط السلوكية (Behavioral Patterns)

### التعريف والهدف:
1. **التركيز**: الأنماط السلوكية تهتم بالخوارزميات والتواصل بينها
2. **المشكلة**: العمليات التي تشكل خوارزمية واحدة قد تكون مقسمة بين فئات مختلفة
3. **النتيجة**: ترتيب معقد صعب الإدارة والصيانة
4. **الحل**: الأنماط السلوكية تلتقط طرق التعبير عن تقسيم العمليات وتحسين التواصل

---

## 🎯 نمط الاستراتيجية (Strategy Pattern)

### التعريف والأمثلة:
- **التعريف**: يعرّف مجموعة من الخوارزميات التي يمكن استخدامها بالتبادل
- **مثال من الواقع**: طرق النقل إلى المطار

### طرق النقل إلى المطار (مثال Strategy):
1. **قيادة السيارة الشخصية** 🚗
2. **أخذ تاكسي** 🚕
3. **حافلة المطار** 🚌
4. **حافلة المدينة** 🚍
5. **خدمة الليموزين** 🚙
6. **المترو والهليكوبتر** 🚁

**النقطة المهمة**: كل هذه الطرق ستوصل المسافر للمطار ويمكن استخدامها بالتبادل. المسافر يختار الاستراتيجية بناءً على التوازن بين التكلفة والراحة والوقت.

### خصائص نمط Strategy:

#### 1. **القصد (Intent)**:
- تعريف عائلة من الخوارزميات وتغليف كل واحدة منها
- جعلها قابلة للتبديل
- السماح للخوارزمية بالتغير بشكل مستقل عن العملاء الذين يستخدمونها
- التقاط التجريد في واجهة ودفن تفاصيل التنفيذ في الفئات المشتقة

#### 2. **المشكلة (Problem)**:
- **مبدأ "مفتوح-مغلق"**: الكيان البرمجي مفتوح للتوسع ومغلق للتعديل
- **الحل**: تغليف تفاصيل الواجهة في فئة أساسية ودفن تفاصيل التنفيذ في الفئات المشتقة
- **الفائدة للعملاء**:
  - لا تأثير عند تغيير عدد الفئات المشتقة
  - لا تأثير عند تغيير تنفيذ فئة مشتقة

#### 3. **التصميم (Design)**:
- **كيان الواجهة**: يمكن أن يمثل فئة أساسية مجردة أو توقعات توقيع الطريقة من العميل
- **الحالة الأولى**: التسلسل الهرمي للوراثة يمثل تعدد الأشكال الديناميكي
- **الحالة الثانية**: كيان الواجهة يمثل كود القالب في العميل والتسلسل الهرمي يمثل تعدد الأشكال الثابت

### كود نمط Strategy بـ C#:

```csharp
class MainApp
{
    static void Main()
    {
        Context context;

        // Three contexts following different strategies
        context = new Context(new ConcreteStrategyA());
        context.ContextInterface();

        context = new Context(new ConcreteStrategyB());
        context.ContextInterface();

        context = new Context(new ConcreteStrategyC());
        context.ContextInterface();

        Console.ReadKey();
    }
}

abstract class Strategy
{
    public abstract void AlgorithmInterface();
}

/// <summary>
/// A 'ConcreteStrategy' class
/// </summary>
class ConcreteStrategyA : Strategy
{
    public override void AlgorithmInterface()
    {
        Console.WriteLine("Called ConcreteStrategyA.AlgorithmInterface()");
    }
}

class ConcreteStrategyB : Strategy
{
    public override void AlgorithmInterface()
    {
        Console.WriteLine("Called ConcreteStrategyB.AlgorithmInterface()");
    }
}

class ConcreteStrategyC : Strategy
{
    public override void AlgorithmInterface()
    {
        Console.WriteLine("Called ConcreteStrategyC.AlgorithmInterface()");
    }
}

/// <summary>
/// The 'Context' class
/// </summary>
class Context
{
    private Strategy _strategy;

    // Constructor
    public Context(Strategy strategy)
    {
        this._strategy = strategy;
    }

    public void ContextInterface()
    {
        _strategy.AlgorithmInterface();
    }
}
```

### كود نمط Strategy بـ Python:

```python
from abc import ABC, abstractmethod

# The 'Strategy' abstract base class
class Strategy(ABC):
    @abstractmethod
    def algorithm_interface(self):
        pass

# ConcreteStrategyA
class ConcreteStrategyA(Strategy):
    def algorithm_interface(self):
        print("Called ConcreteStrategyA.algorithm_interface()")

# ConcreteStrategyB
class ConcreteStrategyB(Strategy):
    def algorithm_interface(self):
        print("Called ConcreteStrategyB.algorithm_interface()")

# ConcreteStrategyC
class ConcreteStrategyC(Strategy):
    def algorithm_interface(self):
        print("Called ConcreteStrategyC.algorithm_interface()")

# The 'Context' class
class Context:
    def __init__(self, strategy: Strategy):
        self._strategy = strategy

    def context_interface(self):
        self._strategy.algorithm_interface()

# The 'MainApp' execution section
if __name__ == "__main__":
    # Three contexts following different strategies
    context = Context(ConcreteStrategyA())
    context.context_interface()

    context = Context(ConcreteStrategyB())
    context.context_interface()

    context = Context(ConcreteStrategyC())
    context.context_interface()
```

---

## 🔄 نمط الحالة (State Pattern)

### الدور والمفهوم:
1. **العلاقة مع Strategy**: يمكن رؤية نمط State كنسخة ديناميكية من نمط Strategy
2. **الهدف**: يسمح للكائن بتغيير سلوكه ديناميكياً حسب حالته الداخلية
3. **الآلية**: عندما تتغير الحالة داخل الكائن، يمكنه تغيير سلوكه بالتبديل إلى مجموعة مختلفة من العمليات
4. **التنفيذ**: يتم التغيير عبر متغير كائن يغير فئته الفرعية ضمن التسلسل الهرمي

### مكونات نمط State:

#### 1. **Context (السياق)**:
- فئة تحتفظ بنسخة من State تعرّف السياق الحالي
- الواجهة المهمة للعملاء

#### 2. **IState (واجهة الحالة)**:
- تعرّف واجهة لحالة معينة من Context

#### 3. **StateA و StateB (الحالات المحددة)**:
- فئات تنفذ السلوك المرتبط بحالة من Context

### مثال عملي - نظام العداد:

```python
import random
from abc import ABC, abstractmethod

# --------------------------
# Interface (IState)
# --------------------------
class IState(ABC):
    @abstractmethod
    def move_up(self, context):
        pass

    @abstractmethod
    def move_down(self, context):
        pass

# Concrete State: NormalState
class NormalState(IState):
    def move_up(self, context):
        if context.counter < Context.limit:
            context.state = AbnormalState()
            print("++", end="")
            context.counter += 1
            return context.counter

    def move_down(self, context):
        if context.counter > 0:
            context.state = AbnormalState()
            print("--", end="")
            context.counter -= 1
            return context.counter

# Concrete State: AbnormalState
class AbnormalState(IState):
    def move_up(self, context):
        if context.counter < Context.limit:
            context.state = NormalState()
            print("||", end="")
            context.counter += 5
            return context.counter

    def move_down(self, context):
        if context.counter < Context.limit:
            context.state = NormalState()
            print("||", end="")
            context.counter -= 5
            return context.counter

# Context Class
class Context:
    limit = 10  # constant

    def __init__(self):
        self.state = None  # current state object
        self.counter = Context.limit  # initialized to limit

    def request(self, n):
        if n == 2:
            return self.state.move_up(self)
        else:
            return self.state.move_down(self)

# --------------------------
# Main Application
# --------------------------
if __name__ == "__main__":
    context = Context()
    context.state = NormalState()
    random.seed(37)

    for i in range(5, 26):
        command = random.randint(0, 2)
        print(context.request(command), end=" ")
    print()
```

### شرح المثال:
- **NormalState**: الحالة العادية حيث العداد يزيد/ينقص بـ 1
- **AbnormalState**: الحالة غير العادية حيث العداد يزيد/ينقص بـ 5
- **التبديل**: الحالة تتغير تلقائياً بناءً على العمليات المنفذة
- **السلوك الديناميكي**: نفس الطلب ينتج سلوكاً مختلفاً حسب الحالة الحالية

---

## 📊 مقارنة شاملة بين جميع الأنماط

| النمط | النوع | الهدف الرئيسي | مثال من الواقع | متى نستخدمه |
|-------|------|-------------|----------------|-------------|
| **Facade** | هيكلي | تبسيط النظم المعقدة | ريموت التلفزيون | نظام معقد يحتاج واجهة بسيطة |
| **Adapter** | هيكلي | ربط الواجهات غير المتوافقة | محول الكهرباء | دمج أنظمة قديمة مع جديدة |
| **Decorator** | هيكلي | إضافة وظائف ديناميكياً | إكسسوارات الهاتف | إضافة ميزات دون تعديل الأصل |
| **Proxy** | هيكلي | التحكم في الوصول | حارس الأمن | تأجيل الإنشاء أو التحكم في الوصول |
| **Strategy** | سلوكي | خوارزميات قابلة للتبديل | طرق النقل للمطار | خوارزميات متعددة لنفس المهمة |
| **State** | سلوكي | تغيير السلوك بالحالة | آلة بيع المشروبات | سلوك يعتمد على الحالة الداخلية |

---

## 🎯 ملخص نهائي للمحاضرة

### 🏛️ **أنماط التصميم - المفاهيم الأساسية**:
- **التعريف**: حلول مُجربة لمشاكل متكررة في البرمجة الكائنية
- **الفلسفة**: مثل الوصفات التي تستخدم مبادئ البرمجة كمكونات
- **الهدف**: تحسين جودة الكود وسهولة الصيانة والتطوير

### 📊 **التصنيف الثلاثي**:
1. **🏗️ هيكلية**: ربط الكائنات (Facade, Adapter, Decorator, Proxy)
2. **🎭 سلوكية**: تنظيم السلوك والتواصل (Strategy, State)
3. **🏭 إنشائية**: إنشاء الكائنات (Singleton, Factory, Abstract Factory)

### 🎯 **الأنماط المدروسة بالتفصيل**:

#### **الأنماط الهيكلية**:
- **Facade**: واجهة موحدة لتبسيط النظم المعقدة
- **Adapter**: ربط الواجهات غير المتوافقة
- **Decorator**: إضافة وظائف ديناميكياً دون تعديل الأصل
- **Proxy**: التحكم في الوصول والإنشاء

#### **الأنماط السلوكية**:
- **Strategy**: عائلة خوارزميات قابلة للتبديل
- **State**: تغيير السلوك حسب الحالة الداخلية

### 💡 **النصائح الذهبية**:
1. **استخدم الواجهات** بدلاً من الفئات عند الإمكان
2. **فكر في المرونة** والقابلية للتوسع
3. **اختر النمط المناسب** للمشكلة المحددة
4. **لا تُفرط** في استخدام الأنماط
5. **الهدف هو التبسيط** وليس التعقيد

### 🎓 **للاختبار والمراجعة**:
- **احفظ الهدف الرئيسي** لكل نمط
- **تذكر الأمثلة العملية** من الواقع
- **افهم متى تستخدم** كل نمط
- **تدرب على كتابة الكود** الأساسي لكل نمط
- **قارن بين الأنماط** المتشابهة

### 🌟 **الخلاصة النهائية**:
أنماط التصميم ليست قوانين صارمة بل **أدوات قوية** تساعد في حل المشاكل الشائعة. الهدف هو **تحسين جودة الكود** وجعله أكثر **مرونة وقابلية للصيانة**. استخدمها بحكمة عندما تواجه المشاكل التي صُممت لحلها.
